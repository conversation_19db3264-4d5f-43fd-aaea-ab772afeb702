#!/usr/bin/env python3
import json
import sys

# Read the complete nodes data
with open('complete_nodes.json', 'r', encoding='utf-8') as f:
    nodes_data = f.read().strip()

# Read the complete connections data  
with open('complete_connections.json', 'r', encoding='utf-8') as f:
    connections_data = f.read().strip()

try:
    # Parse the JSON data
    nodes = json.loads(nodes_data)
    connections = json.loads(connections_data)
    
    # Create the complete workflow object
    workflow = {
        "id": "7EwjfAoKDkj7crK4",
        "name": "My workflow",
        "active": False,
        "nodes": nodes,
        "connections": connections,
        "settings": {"executionOrder": "v1"}
    }
    
    # Write formatted JSON to file
    with open('My_workflow_COMPLETE_EDITABLE.json', 'w', encoding='utf-8') as f:
        json.dump(workflow, f, indent=2, ensure_ascii=False)
    
    print("✅ 完整的工作流 JSON 文件已成功創建：'My_workflow_COMPLETE_EDITABLE.json'")
    print(f"📊 包含 {len(nodes)} 個節點")
    print(f"🔗 包含 {len(connections)} 個連接組")
    
    # Print node names for reference
    print("\n📋 工作流節點列表：")
    for i, node in enumerate(nodes, 1):
        print(f"  {i:2d}. {node['name']} ({node['type']})")
    
except json.JSONDecodeError as e:
    print(f"❌ JSON 解析錯誤: {e}")
except Exception as e:
    print(f"❌ 處理錯誤: {e}")

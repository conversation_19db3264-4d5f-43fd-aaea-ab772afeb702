{"id": "7EwjfAoKDkj7crK4", "name": "My workflow", "active": false, "nodes": [{"parameters": {"jsCode": "const articles = $json.articles || [];\nconst filteredArticles = articles.map(article => ({\n  title: article.title,\n  description: article.description,\n}));\n\nreturn [{\n  json:{\n    filteredArticles\n  }\n}]"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 760], "id": "a2a01538-cadb-4212-beb3-2d61e5e73d3f", "name": "trimming news"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1080, 340], "id": "6ef98fd7-b9c3-4ded-8904-d15997533940", "name": "<PERSON><PERSON>", "alwaysOutputData": false}, {"parameters": {"modelId": {"__rl": true, "value": "deepseek-ai/deepseek-r1", "mode": "list", "cachedResultName": "DEEPSEEK-AI/DEEPSEEK-R1"}, "messages": {"values": [{"content": "=You are a highly intelligent and accurate sentiment analyzer specializing in cryptocurrency markets. Analyze the sentiment of the provided text using a two-part approach: \n\n1. Short-Term Sentiment: \n    -Evaluate the immediate market reaction, recent news impact, and technical volatility. \n    -Determine a sentiment category\"positive\",\"Neutral\", or \"Negative\".\n    -Calculate a numerical score between -1 (extremly negative) and 1 (extremely positive).\n    -Provide a detailed rationale explaining the short-term sentiment. \n\n2. Long-Term Sentiment: \n    -Evaluate the overall market outlook, fundamentals, and regulatory developments. \n    -Determine the sentiment category: \"Positive\", \"Neutral\", or \"Negative\". \n    -Calculate a numerical score between -1 (extremely negative) and 1 (extremely positive). \n    -Provide a detailed rationale explaining the long-term sentiment. \n\nYour output must be exactly a JSON object with exactly two keys: \"shortTermSentiment\" and \"longTermSentiment\". Do not output anything else. \n\nFor example, your output should look like: {\n  \"shortTermSentiment\": {\n    \"category\": \"Positive\", \n    \"score\": 0.7, \n    \"rationale\": \"...\"\n}, \n  \"longTermSentiment\": {\n    \"category\": \"Neutral\", \n    \"score\": 0.1, \n    \"rationale\": \"...\"\n  }\n}. \nNow, analyze the following text and produce your JSON output: {{ JSON.stringify($json.filteredArticles) }}\n\n", "role": "system"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [700, 780], "id": "15c9a24d-ceaf-4cfd-9d98-9f7065def08d", "name": "OpenAI", "retryOnFail": true, "alwaysOutputData": true}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1760, 480], "id": "86654fda-bc20-4491-8207-fdc2671b07c3", "name": "Merge1", "alwaysOutputData": true}, {"parameters": {"jsCode": "// 獲取所有輸入項目\nconst allCandles = [];\n\nfor (const item of items) {\n  allCandles.push(item.json);\n}\n\n//Return a single item with a property named \"allCandles\" (an array of 3000)\nreturn [{\n  json: {\n    allCandles\n  }\n}]"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1460, 360], "id": "601859ec-74cf-427c-8fbe-ad3c8c52a97c", "name": "combine JSON"}, {"parameters": {"jsCode": "const deepSeekrResponseRaw = $input.first().json.message.content.trim();\n\n// 找到第一個 { 的位置，假設 JSON 內容從此開始\nconst jsonStartIndex = deepSeekrResponseRaw.indexOf('{');\nif (jsonStartIndex === -1) {\n  throw new Error(\"無法在回應中找到 JSON 開頭\");\n}\n\nconst jsonEndIndex = deepSeekrResponseRaw.lastIndexOf('}') + 1;\nconst jsonPart = deepSeekrResponseRaw.substring(jsonStartIndex);\n\ntry {\n  // Parse the extracted JSON content\n  const parsedResponse = JSON.parse(deepSeekrResponseRaw.substring(jsonStartIndex, jsonEndIndex));\n  \n  // 以 N8N 格式回傳結果\n  return [{ \n    json: {\n      shortTermSentiment: parsedResponse.shortTermSentiment,\n      longTermSentiment: parsedResponse.longTermSentiment\n    } \n  }];\n} catch (error) {\n  throw new Error(\"解析 DeepSeekr1 回應時發生錯誤: \" + error);\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 780], "id": "1cd7a14b-65bf-45f3-983e-d41ab00a73bd", "name": "檢驗"}, {"parameters": {"url": "=https://api.binance.com/api/v3/klines?symbol={{ $json.tradingPairSymbol }}&interval=1h&limit=200", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 400], "id": "fcd403ea-acd0-43f6-9a80-f3255249c1cd", "name": "HTTP Request 1h1"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-160, 400], "id": "9e736d1b-97e1-431a-860e-111abc2f6c92", "name": "Telegram Trigger1", "webhookId": "a4854430-b293-4ab9-8ec4-59c812252b3b"}], "connections": {}, "settings": {"executionOrder": "v1"}}
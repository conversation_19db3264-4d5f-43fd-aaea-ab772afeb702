{"version": 3, "file": "patterndetection.js", "sourceRoot": "", "sources": ["../../src/patterndetection/patterndetection.ts"], "names": [], "mappings": ";;;;;;;;AAAA,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AACtC,OAAO,OAAO,MAAM,UAAU,CAAC;AAE/B,IAAI,iBAAiB,GAAG,KAAK,CAAC;AAM9B,IAAI,CAAC;IACD,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,kBAAkB,CAAA;AAC5F,CAAC;AAAC,KAAK,CAAA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC;AAEd,IAAI,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,iBAAiB,CAAC;AAE7D,IAAI,KAAK,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC;IAC1B,QAAQ,EAAE,iBAAiB,CAAC,CAAC,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,SAAS;IAClE,GAAG,EAAE,KAAK;IACV,UAAU,EAAE,iBAAiB;CAChC,CAAC,CAAA;AAEF,MAAM,2BAA4B,SAAQ,cAAc;IACpD,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QADO,WAAM,GAAN,MAAM,CAAS;IAElC,CAAC;CACJ;AAED,MAAM,CAAN,IAAY,iBAOX;AAPD,WAAY,iBAAiB;IACzB,qDAAI,CAAA;IACJ,uDAAK,CAAA;IACL,qDAAI,CAAA;IACJ,qDAAI,CAAA;IACJ,qDAAI,CAAA;IACJ,qDAAI,CAAA;AACR,CAAC,EAPW,iBAAiB,KAAjB,iBAAiB,QAO5B;AAED,0BAA0B,IAAQ,EAAE,QAAY;IAC5C,IAAI,iBAAiB,GAAG,UAAU,MAAU,EAAE,KAAS,EAAE,OAAW;QAChE,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC;IAC/C,CAAC,CAAC;IAEF,IAAI,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC;IAC1B,IAAI,YAAY,GAAO,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;IACtE,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;IAC3C,GAAG,CAAC,CAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,GAAG,GAAG,CAAC,GAAG,YAAY,CAAC;QAC3B,IAAI,MAAM,GAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACvD,IAAI,KAAK,GAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACrD,IAAI,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC;QAC3B,OAAO,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IACvE,CAAC;IACD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB;IACpE,MAAM,CAAC,OAAO,CAAC;AACnB,CAAC;AAAA,CAAC;AAEF,qBAAqB,GAAO;IACxB,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAO,EAAE,KAAS,EAAC,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjF,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAK,EAAC,EAAE,CAAA,CAAC,GAAC,IAAI,CAAC,CAAC;AACpC,CAAC;AAED,MAAM;CAIL;AAED,MAAM,yBAA+B,KAA0B;;QACvD,EAAE,CAAA,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,sFAAsF,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC7H,CAAC;QACD,MAAM,KAAK,CAAC,KAAK,EAAE,CAAA;QACnB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;QACxB,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QACtD,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC;YAC7B,KAAK,EAAG,IAAI,YAAY,CAAC,MAAM,CAAC;SACnC,CAAC,CAAA;QACF,IAAI,KAAK,GAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7D,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/B,MAAM,CAAC;YACH,OAAO,EAAG,iBAAiB,CAAC,KAAK,CAAQ;YACzC,SAAS,EAAE,KAAK;YAChB,WAAW,EAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG;SAC3C,CAAA;IACT,CAAC;CAAA;AAED,MAAM,0BAAgC,KAA0B;;QAC5D,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,iBAAiB,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA;IACjF,CAAC;CAAA;AAED,MAAM,uBAA6B,KAA0B;;QACzD,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,iBAAiB,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA;IACjF,CAAC;CAAA;AAED,MAAM,6BAAmC,KAA0B;;QAC/D,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,iBAAiB,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA;IACjF,CAAC;CAAA;AAED,MAAM,oCAA0C,KAA0B;;QACtE,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,iBAAiB,CAAC,GAAG,IAAI,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA;IAClF,CAAC;CAAA;AAED,MAAM,uBAA6B,KAA0B;;QACzD,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,iBAAiB,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA;IACjF,CAAC;CAAA;AAED,MAAM,yBAA+B,KAA0B;;QAC3D,IAAI,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,KAAK,iBAAiB,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,CAAA;IACjF,CAAC;CAAA;AAED,MAAM,sBAAuB,SAAQ,SAAS;;AACnC,8BAAc,GAAG,cAAc,CAAC;AAChC,+BAAe,GAAG,eAAe,CAAC;AAClC,4BAAY,GAAG,YAAY,CAAC;AAC5B,kCAAkB,GAAG,kBAAkB,CAAC;AACxC,yCAAyB,GAAG,yBAAyB,CAAC;AACtD,4BAAY,GAAG,YAAY,CAAC;AAC5B,8BAAc,GAAG,cAAc,CAAC"}
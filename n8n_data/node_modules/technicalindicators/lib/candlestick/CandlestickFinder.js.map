{"version": 3, "file": "CandlestickFinder.js", "sourceRoot": "", "sources": ["../../src/candlestick/CandlestickFinder.ts"], "names": [], "mappings": "AACA,MAAM,CAAC,OAAO;IAGV;QACI,iCAAiC;QACjC,6CAA6C;QAC7C,IAAI;IACR,CAAC;IACD,gBAAgB,CAAC,CAAQ,EAAE,CAAQ;QAC/B,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1D,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,MAAM,CAAE,IAAI,IAAI,KAAK,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,IAAc;QAChB,MAAM,4BAA4B,CAAC;IACvC,CAAC;IACD,kBAAkB,CAAE,IAAc;QAC9B,EAAE,CAAA,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,sDAAsD,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,MAAM,CAAC,EAAE,CAAC;QACd,CAAC;QACD,EAAE,CAAA,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;aACpC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACZ,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAC9D,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnB,MAAM,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC,CAAC;IACvB,CAAC;IAED,UAAU,CAAE,IAAc;QACtB,EAAE,CAAA,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,sDAAsD,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,MAAM,CAAC,KAAK,CAAC;QACjB,CAAC;QACD,EAAE,CAAA,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAES,0BAA0B,CAAC,IAAc;QAC/C,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,CAAC;QAChB,CAAC;QAAC,IAAI,CAAC,CAAC;YACJ,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAG,EAAE;gBACT,IAAI,EAAG,EAAE;gBACT,GAAG,EAAI,EAAE;gBACT,KAAK,EAAE,EAAE;aACC,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC;YAC9C,OAAO,CAAC,GAAG,aAAa,EAAE,CAAC;gBACvB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,EAAE,CAAC;YACR,CAAC;YACD,MAAM,CAAC,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAES,2BAA2B,CAAC,IAAc;QAC5C,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvC,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAS,WAAW,EAAE,KAAK;YAC1D,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAG,EAAE;gBACT,IAAI,EAAG,EAAE;gBACT,GAAG,EAAI,EAAE;gBACT,KAAK,EAAE,EAAE;aACC,CAAC;YACf,OAAM,CAAC,GAAG,aAAa,EAAE,CAAC;gBACtB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,EAAE,CAAC;YACR,CAAC;YACD,MAAM,CAAC,SAAS,CAAC;QACrB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC;QACtF,MAAM,CAAC,aAAa,CAAC;IAC7B,CAAC;CACJ"}
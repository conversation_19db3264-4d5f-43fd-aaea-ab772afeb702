{"version": 3, "file": "MACD.js", "sourceRoot": "", "sources": ["../../src/moving_averages/MACD.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAE5B,MAAM,gBAAiB,SAAQ,cAAc;IAMzC,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QADO,WAAM,GAAN,MAAM,CAAS;QALlC,uBAAkB,GAAW,IAAI,CAAC;QAClC,mBAAc,GAAW,IAAI,CAAC;IAM9B,CAAC;CACJ;AAED,MAAM;CAIL;AAED,MAAM,WAAY,SAAQ,SAAS;IAG/B,YAAY,KAAe;QACzB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5D,IAAI,YAAY,GAAK,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACtD,IAAI,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAC,MAAM,EAAG,KAAK,CAAC,UAAU,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAChH,IAAI,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAC,MAAM,EAAG,KAAK,CAAC,UAAU,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAChH,IAAI,gBAAgB,GAAG,IAAI,YAAY,CAAC,EAAC,MAAM,EAAG,KAAK,CAAC,YAAY,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAChH,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,CAAC;YACT,IAAI,IAAqB,EAAE,MAAuB,EAAE,SAA0B,EAAE,IAAqB,EAAE,IAAqB,CAAC;YAC7H,OAAO,IAAI,EAAE,CAAC;gBACZ,EAAE,CAAA,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,CAAA,CAAC;oBAC3B,IAAI,GAAG,KAAK,CAAC;oBACb,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACtC,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACtC,KAAK,EAAE,CAAC;oBACR,QAAQ,CAAC;gBACX,CAAC;gBACD,EAAE,CAAA,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC;oBAChB,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;oBACnB,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5C,CAAC;gBACD,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC;gBAC1B,IAAI,GAAG,MAAK,CAAC;oBACX,cAAc;oBACd,cAAc;oBACd,IAAI,EAAG,MAAM,CAAC,IAAI,CAAC;oBACnB,MAAM,EAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;oBAC5C,SAAS,EAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;iBAC7D,CAAC,CAAA;gBACF,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAID,SAAS,CAAC,KAAY;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,MAAM,CAAC,MAAM,CAAC;IAClB,CAAC;IAAA,CAAC;;AALK,cAAS,GAAC,IAAI,CAAC;AAQ1B,MAAM,eAAe,KAAe;IAC7B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}
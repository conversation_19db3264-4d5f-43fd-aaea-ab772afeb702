{"version": 3, "file": "Renko.js", "sourceRoot": "", "sources": ["../../src/chart_types/Renko.ts"], "names": [], "mappings": "AAAA,OAAO,EAAc,UAAU,EAAE,MAAM,cAAc,CAAC;AACtD,OAAO,EAAE,GAAG,EAAE,MAAM,4BAA4B,CAAC;AAEjD;;GAEG;AACH,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAEnE,MAAM,iBAAkB,SAAQ,cAAc;CAU7C;AAED,WAAY,SAAQ,SAAS;IAGzB,YAAY,KAAgB;QAC1B,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;QACrC,EAAE,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,IAAI,SAAS,GAAG,GAAG,CAAO,MAAO,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YACrD,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAAA,CAAC;QAChC,EAAE,CAAA,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,OAAO,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;YACjF,MAAM,CAAC;QACX,CAAC;QACD,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,OAAO,GAAG,QAAQ,CAAC;QACvB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACvB,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,OAAO,IAAI,EAAE,CAAC;gBACZ,uBAAuB;gBACvB,EAAE,CAAA,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC;oBAChB,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC5B,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;oBAC3B,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;oBACzB,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC7B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;oBAC/B,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC;oBACrC,UAAU,GAAG,KAAK,CAAC;oBACnB,QAAQ,CAAC;gBACb,CAAC;gBACD,IAAI,yBAAyB,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;gBACvE,IAAI,wBAAwB,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;gBAErE,EAAE,CAAA,CAAC,CAAC,yBAAyB,IAAI,SAAS,CAAC,IAAI,CAAC,wBAAwB,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC;oBACrF,IAAI,SAAS,GAAG,yBAAyB,GAAG,wBAAwB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAA;oBAC3F,IAAI,UAAU,GAAQ;wBAClB,IAAI,EAAG,SAAS;wBAChB,IAAI,EAAG,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI;wBAC9D,GAAG,EAAG,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG;wBACzD,KAAK,EAAG,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;wBACxF,MAAM,EAAG,UAAU,GAAG,UAAU,CAAC,MAAM;wBACvC,SAAS,EAAG,UAAU,CAAC,SAAS;qBACnC,CAAC;oBACF,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;oBAC3B,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC5B,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC3B,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC7B,UAAU,GAAG,CAAC,CAAC;oBACf,UAAU,GAAG,MAAM,UAAU,CAAA;gBACjC,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACJ,QAAQ,GAAG,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;oBACnE,OAAO,GAAG,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;oBAC9D,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;oBAC5C,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC;oBACrC,UAAU,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC;QACL,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACxB,IAAI,EAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACxB,GAAG,EAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBACtB,KAAK,EAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC1B,MAAM,EAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC5B,SAAS,EAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;aACrC,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBACxC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;gBAC1C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAC5C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACtD,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAID,SAAS,CAAC,KAAY;QAClB,OAAO,CAAC,KAAK,CAAC,+GAA+G,CAAC,CAAC;QAC/H,MAAM,CAAC,IAAI,CAAC;IAChB,CAAC;IAAA,CAAC;;AALK,eAAS,GAAC,KAAK,CAAC;AAQ3B,MAAM,gBAAgB,KAAgB;IAC/B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACrC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACrB,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}
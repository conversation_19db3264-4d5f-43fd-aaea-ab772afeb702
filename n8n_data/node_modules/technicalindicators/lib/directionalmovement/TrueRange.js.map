{"version": 3, "file": "TrueRange.js", "sourceRoot": "", "sources": ["../../src/directionalmovement/TrueRange.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE;;GAEG;AACH;;GAEG;AACH,YAAY,CAAA;AACZ,MAAM,qBAAsB,SAAQ,cAAc;CAIjD;AAAA,CAAC;AAEF,MAAM,gBAAiB,SAAQ,SAAS;IAGtC,YAAY,KAAoB;QAC9B,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,EAAE,CAAA,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,oCAAoC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,OAAO,GAAc,KAAK,CAAC;YAC/B,IAAI,aAAa,EAAC,MAAM,CAAC;YACzB,OAAO,IAAI,EAAE,CAAC;gBACZ,EAAE,CAAA,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC;oBAC/B,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;oBAC9B,OAAO,GAAG,MAAM,MAAM,CAAC;gBACzB,CAAC;gBACD,MAAM,GAAG,IAAI,CAAC,GAAG,CACb,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,EAC1B,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,EAC1F,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,aAAa,CAAC,CAC3F,CAAC;gBACF,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC9B,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAA,CAAC;oBACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;gBACzB,CAAC;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;YACzB,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAG,KAAK,CAAC,KAAK,CAAC;gBACnB,GAAG,EAAI,IAAI,CAAC,KAAK,CAAC;gBAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACrB,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAA,CAAC;IAIF,SAAS,CAAC,KAAgB;QACvB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;IAC3C,CAAC;IAAA,CAAC;;AAJK,mBAAS,GAAG,SAAS,CAAC;AAQ/B,MAAM,oBAAoB,KAAoB;IAC1C,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACzC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}
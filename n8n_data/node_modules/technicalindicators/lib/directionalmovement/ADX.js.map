{"version": 3, "file": "ADX.js", "sourceRoot": "", "sources": ["../../src/directionalmovement/ADX.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAC;AACrE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,GAAG,EAAE,MAAQ,WAAW,CAAC;AAClC,OAAO,EAAE,GAAG,EAAE,MAAS,UAAU,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,EAAE,IAAI,EAAE,MAAY,yBAAyB,CAAC;AAErD,MAAM,eAAgB,SAAQ,cAAc;CAK3C;AAAA,CAAC;AAEF,MAAM,gBAAiB,SAAQ,cAAc;CAI5C;AAAA,CAAC;AAIF,MAAM,UAAW,SAAQ,SAAS;IAGhC,YAAY,KAAc;QACxB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;YACnB,IAAI,EAAE,EAAE;YACR,GAAG,EAAG,EAAE;SACT,CAAC,CAAC;QAEH,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC;YACpB,IAAI,EAAE,EAAE;YACR,GAAG,EAAG,EAAE;SACT,CAAC,CAAC;QAEH,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAC,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAC1F,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAC,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAC1F,IAAI,KAAK,GAAI,IAAI,eAAe,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAC,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAC1F,IAAI,KAAK,GAAI,IAAI,IAAI,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAC,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAE/E,IAAI,EAAE,GAAM,IAAI,SAAS,CAAC;YACxB,GAAG,EAAG,EAAE;YACR,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;QAEH,EAAE,CAAA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAE,CAAC,CAAA,CAAC;YACzE,MAAM,CAAC,2CAA2C,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACrB,SAAS,CAAA;QACL,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,UAAU,CAAC;YAChE,OAAO,GAAG,CAAC,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC;YACb,QAAQ,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,EAAE,CAAC;gBACZ,IAAI,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,EAAE,CAAA,CAAC,MAAM,KAAK,SAAS,CAAC,CAAA,CAAC;oBACvB,IAAI,GAAG,KAAK,CAAC;oBACb,QAAQ,CAAC;gBACX,CAAC;gBACD,IAAI,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBACrC,IAAI,QAAQ,GAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC1C,IAAI,QAAQ,GAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC1C,EAAE,CAAA,CAAC,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC;oBAChF,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC;oBACrC,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC;oBACrC,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC;oBACzC,IAAI,KAAK,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC;oBAChC,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;oBAChC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;oBACpC,2SAA2S;gBAC7S,CAAC;gBACD,IAAI,GAAG,MAAM,EAAE,GAAG,EAAG,UAAU,EAAE,GAAG,EAAG,OAAO,EAAE,GAAG,EAAG,OAAO,EAAE,CAAC;YAClE,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAC,KAAK,EAAE,EAAE;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAG,KAAK,CAAC,KAAK,CAAC;gBACnB,GAAG,EAAI,IAAI,CAAC,KAAK,CAAC;gBAClB,KAAK,EAAG,MAAM,CAAC,KAAK,CAAC;aACtB,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACvH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IA/EiD,CAAC;IA+ElD,CAAC;IAIF,SAAS,CAAC,KAAY;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,EAAE,GAAG,EAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;QACzG,CAAC;IACL,CAAC;IAAA,CAAC;;AAPK,aAAS,GAAG,GAAG,CAAC;AAUzB,MAAM,cAAc,KAAc;IAC9B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAAA,CAAC"}
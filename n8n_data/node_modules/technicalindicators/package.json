{"name": "technicalindicators", "version": "3.1.0", "description": "Techincal Indicators written in javascript", "main": "dist/index.js", "module": "lib/index.js", "types": "./declarations/index.d.ts", "jsnext:main": "lib/index.js", "scripts": {"cover": "babel-istanbul --include-all-sources cover -x dist/**/*.* ./node_modules/mocha/bin/_mocha -- -R spec && open coverage/lcov-report/index.html", "test": "mocha --compilers js:babel-register --require babel-polyfill", "test:watch": "mocha --watch --compilers js:babel-register --require babel-polyfill", "generateDts": "./generateTsDefinitions.sh", "build": "tsc -p . && babel-node --presets es2015 rollup.js", "start": "sh test.sh"}, "repository": {"type": "git", "url": "git+https://github.com/anandanand84/technicalindicators.git"}, "keywords": ["Technical indicators", "Technical analysis", "finance", "forex", "stocks", "Bitcoin", "Ethereum", "Crypto Currency", "rsi", "relative strength index", "macd", "moving average convergence divergance and histogram", "ema", "exponential moving average", "Awesome Oscillator", "Keltner Channels", "Ichimoku Cloud", "AO", "sma", "simple moving average", "wma", "weighted moving average", "kst", "know sure thing", "roc", "rate of change", "bollinger bands", "atr", "Average true range", "Stochastic", "WilliamsR", "Accumulation Distribution Line", "adl", "On Balance Volume", "OBV", "TRIX", "CandleStick", "StochRSI", "Stochastic RSI", "DOJI", "AbandonedBaby", "<PERSON><PERSON>", "ADX", "<PERSON><PERSON><PERSON>", "CCI", "Commodity Channel Index", "VWAP", "Volume Profile", "Force Index", "Money Flow Index", "Typical Price", "PSAR", "Pattern detection", "Head and Shoulder", "Inverse Head and Shoulder", "Double Top", "Double Bottom", "Trending Up", "Trending Down", "Custom Screening", "Highest", "Lowest", "Standard Deviation", "Average Gain", "Average Loss", "Bullish Hammer", "Bearish Hammer", "Bullish Inverted Hammer", "Bearish Inverted Hammer", "<PERSON>", "Confirmed <PERSON>", "Unconfirmed <PERSON>", "Inverted <PERSON>", "Confirmed Inverted <PERSON>", "Unconfirmed Inverted <PERSON>", "Hanging Man", "Shooting Star", "Tweezer Top", "Tweezer <PERSON>", "Cross Up", "Cross Down", "Cross Over"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/anandanand84/technicalindicators/issues"}, "sideEffects": false, "homepage": "https://github.com/anandanand84/technicalindicators#readme", "devDependencies": {"@types/chai": "^3.0.0", "@types/mocha": "^2.0.0", "@types/node": "^6.0.31", "babel-cli": "^6.8.0", "babel-core": "^6.8.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2015-rollup": "^3.0.0", "babel-register": "^6.8.0", "babel-runtime": "^6.23.0", "chai": "^3.0.0", "draw-candlestick": "2.0.3", "dts-bundle": "^0.7.3", "gulp": "^3.9.1", "gulp-mocha": "^2.2.0", "gulp-util": "^3.0.7", "inquirer": "^5.1.0", "koa": "^2.2.0", "koa-static": "^3.0.0", "lit-html": "^0.9.0", "mocha": "^2.4.5", "monaco-editor": "^0.8.3", "rimraf": "^2.0.0", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-babel-minify": "^3.1.2", "rollup-plugin-commonjs": "^8.3.0", "rollup-plugin-ignore": "^1.0.3", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-typescript2": "^0.18.0", "rollup-plugin-uglify": "^1.0.2", "rollup-watch": "^3.2.2", "ts-node": "^1.0.0", "tslint": "^4.0.0", "typescript": "^2.9.2"}, "dependencies": {"@types/node": "^6.0.96"}}
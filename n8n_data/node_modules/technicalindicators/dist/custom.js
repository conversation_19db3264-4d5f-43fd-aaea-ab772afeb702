(function(a){'use strict';function b(a){return s[a]}function c(a){let c=b('precision');return c?parseFloat(a.toPrecision(c)):a}function d(a){u.reverseInputs(a);var b=new z(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function e(a){u.reverseInputs(a);var b=new A(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function f(a,b,c,d){return a<=c&&b>=c||c<=a&&d>=a}function g(a){u.reverseInputs(a);var b=new B(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function h(a){u.reverseInputs(a);var b=new C(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function i(a){u.reverseInputs(a);var b=new D(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function j(a){u.reverseInputs(a);var b=new H(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function k(a){u.reverseInputs(a);var b=new I(a).result;return a.reversedInput&&(b.open.reverse(),b.high.reverse(),b.low.reverse(),b.close.reverse(),b.volume.reverse(),b.timestamp.reverse()),u.reverseInputs(a),b}function l(a){u.reverseInputs(a);var b=new J(a).result;return a.reversedInput&&(b.open.reverse(),b.high.reverse(),b.low.reverse(),b.close.reverse(),b.volume.reverse(),b.timestamp.reverse()),u.reverseInputs(a),b}function m(a){u.reverseInputs(a);var b=new L(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function n(a){u.reverseInputs(a);var b=new M(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}function o(a){u.reverseInputs(a);var b=new N(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b}var p=Math.abs,q=Math.min,r=Math.max;let s={};class t{}class u{constructor(a){this.format=a.format||c}static reverseInputs(a){a.reversedInput&&(a.values?a.values.reverse():void 0,a.open?a.open.reverse():void 0,a.high?a.high.reverse():void 0,a.low?a.low.reverse():void 0,a.close?a.close.reverse():void 0,a.volume?a.volume.reverse():void 0,a.timestamp?a.timestamp.reverse():void 0)}getResult(){return this.result}}class v{constructor(a,b,c){this.next=c,c&&(c.prev=this),this.prev=b,b&&(b.next=this),this.data=a}}class w{constructor(){this._length=0}get head(){return this._head&&this._head.data}get tail(){return this._tail&&this._tail.data}get current(){return this._current&&this._current.data}get length(){return this._length}push(a){this._tail=new v(a,this._tail),0===this._length&&(this._head=this._tail,this._current=this._head,this._next=this._head),this._length++}pop(){var a=this._tail;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,a.data):(this._tail=a.prev,this._tail.next=void 0,this._current===a&&(this._current=this._tail,this._next=void 0),a.data)}shift(){var a=this._head;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,a.data):(this._head=this._head.next,this._current===a&&(this._current=this._head,this._next=this._current.next),a.data)}unshift(a){this._head=new v(a,void 0,this._head),0===this._length&&(this._tail=this._head,this._next=this._head),this._length++}unshiftCurrent(){var a=this._current;return a===this._head||2>this._length?a&&a.data:(a===this._tail?(this._tail=a.prev,this._tail.next=void 0,this._current=this._tail):(a.next.prev=a.prev,a.prev.next=a.next,this._current=a.prev),this._next=this._current.next,a.next=this._head,a.prev=void 0,this._head.prev=a,this._head=a,a.data)}removeCurrent(){var a=this._current;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,a.data):(a===this._tail?(this._tail=a.prev,this._tail.next=void 0,this._current=this._tail):a===this._head?(this._head=a.next,this._head.prev=void 0,this._current=this._head):(a.next.prev=a.prev,a.prev.next=a.next,this._current=a.prev),this._next=this._current.next,a.data)}resetCursor(){return this._current=this._next=this._head,this}next(){var a=this._next;if(void 0!==a)return this._next=a.next,this._current=a,a.data}}class x extends u{constructor(a){super(a),this.period=a.period,this.price=a.values;this.generator=function*(a){var b,c=new w,d=0,e=1,f=yield;for(c.push(0);;)e<a?(e++,c.push(f),d+=f):(d=d-c.shift()+f,b=d/a,c.push(f)),f=yield b}(this.period),this.generator.next(),this.result=[],this.price.forEach((a)=>{var b=this.generator.next(a);b.value!==void 0&&this.result.push(this.format(b.value))})}nextValue(a){var b=this.generator.next(a).value;if(b!=void 0)return this.format(b)}}x.calculate=function(a){u.reverseInputs(a);var b=new x(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b};class y extends u{constructor(a){super(a);var b,c=a.period,d=a.values;this.result=[],b=new x({period:c,values:[]});this.generator=function*(){for(var a,d=yield;;)void 0!=a&&void 0!==d?(a=(d-a)*(2/(c+1))+a,d=yield a):(d=yield,a=b.nextValue(d),a&&(d=yield a))}(),this.generator.next(),this.generator.next(),d.forEach((a)=>{var b=this.generator.next(a);b.value!=void 0&&this.result.push(this.format(b.value))})}nextValue(a){var b=this.generator.next(a).value;if(b!=void 0)return this.format(b)}}y.calculate=function(a){u.reverseInputs(a);var b=new y(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b};class z extends u{constructor(a){super(a);var b=a.close,c=a.volume,d=a.period||1;if(c.length!==b.length)throw'Inputs(volume, close) not of equal size';let e=new y({values:[],period:d});this.result=[],this.generator=function*(){var a=yield,b=yield;for(let c;;)c=(b.close-a.close)*b.volume,a=b,b=yield e.nextValue(c)}(),this.generator.next(),c.forEach((a,d)=>{var e=this.generator.next({close:b[d],volume:c[d]});e.value!=void 0&&this.result.push(e.value)})}nextValue(a){let b=this.generator.next(a).value;if(b!=void 0)return b}}z.calculate=d;class A extends u{constructor(a){super(a);var b=a.low,c=a.high,d=a.close,e=a.volume,f=this.format;if(b.length!==c.length||c.length!==d.length)throw'Inputs(low,high, close) not of equal size';this.result=[],this.generator=function*(){var a=yield;for(let b=0,c=0;;){let d=(a.high+a.low+a.close)/3,e=a.volume*d;b+=e,c+=a.volume,a=yield b/c}}(),this.generator.next(),b.forEach((a,f)=>{var g=this.generator.next({high:c[f],low:b[f],close:d[f],volume:e[f]});g.value!=void 0&&this.result.push(g.value)})}nextValue(a){let b=this.generator.next(a).value;if(b!=void 0)return b}}A.calculate=e;class B extends u{constructor(a){super(a);var b=a.high,c=a.low,d=a.close,e=a.open,g=a.volume,h=a.noOfBars;if(c.length!==b.length||b.length!==d.length||b.length!==g.length)throw'Inputs(low,high, close, volumes) not of equal size';this.result=[];var i=r(...b,...c,...d,...e),j=q(...b,...c,...d,...e),k=j;for(let l=0;l<h;l++){let a=k,l=a+(i-j)/h;k=l;let m=0,n=0,o=0;for(let h=0;h<b.length;h++){let i=c[h],j=b[h],k=e[h],p=d[h],q=g[h];f(a,l,i,j)&&(o+=q,k>p?n+=q:m+=q)}this.result.push({rangeStart:a,rangeEnd:l,bullishVolume:m,bearishVolume:n,totalVolume:o})}}nextValue(){throw'Next value not supported for volume profile'}}B.calculate=g;class C extends u{constructor(a){super(a);let b=a.values,c=a.period,d=this.format;this.generator=function*(a){var b,c,e=yield,f=1,g=0,h=e;for(e=yield;;)c=e-h,c=0<c?c:0,0<c&&(g+=c),f<a?f++:void 0==b?b=g/a:b=(b*(a-1)+c)/a,h=e,b=void 0===b?void 0:d(b),e=yield b}(c),this.generator.next(),this.result=[],b.forEach((a)=>{var b=this.generator.next(a);b.value!==void 0&&this.result.push(b.value)})}nextValue(a){return this.generator.next(a).value}}C.calculate=h;class D extends u{constructor(a){super(a);let b=a.values,c=a.period,d=this.format;this.generator=function*(a){var b,c,e=yield,f=1,g=0,h=e;for(e=yield;;)c=h-e,c=0<c?c:0,0<c&&(g+=c),f<a?f++:void 0==b?b=g/a:b=(b*(a-1)+c)/a,h=e,b=void 0===b?void 0:d(b),e=yield b}(c),this.generator.next(),this.result=[],b.forEach((a)=>{var b=this.generator.next(a);b.value!==void 0&&this.result.push(b.value)})}nextValue(a){return this.generator.next(a).value}}D.calculate=i;class E{constructor(){this.open=[],this.high=[],this.low=[],this.close=[],this.volume=[],this.timestamp=[]}}class F extends u{constructor(a){super(a);var b,c=a.period,d=a.values;this.result=[],b=new x({period:c,values:[]});this.generator=function*(){for(var a,d=yield;;)void 0!=a&&void 0!==d?(a=(d-a)*(1/c)+a,d=yield a):(d=yield,a=b.nextValue(d),void 0!==a&&(d=yield a))}(),this.generator.next(),this.generator.next(),d.forEach((a)=>{var b=this.generator.next(a);b.value!=void 0&&this.result.push(this.format(b.value))})}nextValue(a){var b=this.generator.next(a).value;if(b!=void 0)return this.format(b)}}F.calculate=function(a){u.reverseInputs(a);var b=new F(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b};class G extends u{constructor(a){super(a);var b=a.low,c=a.high,d=a.close,e=this.format;if(b.length!=c.length)throw'Inputs(low,high) not of equal size';this.result=[],this.generator=function*(){for(var a,b,c=yield;;)void 0==a&&(a=c.close,c=yield b),b=r(c.high-c.low,isNaN(p(c.high-a))?0:p(c.high-a),isNaN(p(c.low-a))?0:p(c.low-a)),a=c.close,void 0!=b&&(b=e(b)),c=yield b}(),this.generator.next(),b.forEach((a,e)=>{var f=this.generator.next({high:c[e],low:b[e],close:d[e]});f.value!=void 0&&this.result.push(f.value)})}nextValue(a){return this.generator.next(a).value}}G.calculate=function(a){u.reverseInputs(a);var b=new G(a).result;return a.reversedInput&&b.reverse(),u.reverseInputs(a),b};class H extends u{constructor(a){super(a);var b=a.low,c=a.high,d=a.close,e=a.period,f=this.format;if(b.length!==c.length||c.length!==d.length)throw'Inputs(low,high, close) not of equal size';var g=new G({low:[],high:[],close:[]}),h=new F({period:e,values:[],format:(a)=>a});this.result=[],this.generator=function*(){for(var a,b,c=yield;;)b=g.nextValue({low:c.low,high:c.high,close:c.close}),a=void 0===b?void 0:h.nextValue(b),c=yield a}(),this.generator.next(),b.forEach((a,e)=>{var g=this.generator.next({high:c[e],low:b[e],close:d[e]});g.value!==void 0&&this.result.push(f(g.value))})}nextValue(a){return this.generator.next(a).value}}H.calculate=j;class I extends u{constructor(a){super(a);this.format;let b=a.useATR,c=a.brickSize||0;if(b){let b=j(Object.assign({},a));c=b[b.length-1]}if(this.result=new E,0===c)return void console.error('Not enough data to calculate brickSize for renko when using ATR');let d=0,e=0,f=Infinity,g=0,h=0,i=0;this.generator=function*(){for(let a=yield;;){if(0==d){d=a.close,e=a.high,f=a.low,g=a.close,h=a.volume,i=a.timestamp,a=yield;continue}let b=p(a.close-g),j=p(a.close-d);if(b>=c&&j>=c){let i=b>j?d:g,k={open:i,high:e>a.high?e:a.high,low:f<a.Low?f:a.low,close:i>a.close?i-c:i+c,volume:h+a.volume,timestamp:a.timestamp};d=k.open,e=k.close,f=k.close,g=k.close,h=0,a=yield k}else e=e>a.high?e:a.high,f=f<a.Low?f:a.low,h+=a.volume,i=a.timestamp,a=yield}}(),this.generator.next(),a.low.forEach((b,c)=>{var d=this.generator.next({open:a.open[c],high:a.high[c],low:a.low[c],close:a.close[c],volume:a.volume[c],timestamp:a.timestamp[c]});d.value&&(this.result.open.push(d.value.open),this.result.high.push(d.value.high),this.result.low.push(d.value.low),this.result.close.push(d.value.close),this.result.volume.push(d.value.volume),this.result.timestamp.push(d.value.timestamp))})}nextValue(){return console.error('Cannot calculate next value on Renko, Every value has to be recomputed for every change, use calcualte method'),null}}I.calculate=k;class J extends u{constructor(a){super(a);this.format;this.result=new E;let b=null,c=0,d=Infinity,e=0,f=0,g=0;this.generator=function*(){for(let a=yield,h=null;;){if(null==b)b=(a.close+a.open)/2,c=a.high,d=a.low,e=(a.close+a.open+a.high+a.low)/4,f=a.volume||0,g=a.timestamp||0,h={open:b,high:c,low:d,close:e,volume:a.volume||0,timestamp:a.timestamp||0};else{let f=(a.close+a.open+a.high+a.low)/4,g=(b+e)/2,i=r(g,f,a.high),j=q(a.low,g,f);h={close:f,open:g,high:i,low:j,volume:a.volume||0,timestamp:a.timestamp||0},e=f,b=g,c=i,d=j}a=yield h}}(),this.generator.next(),a.low.forEach((b,c)=>{var d=this.generator.next({open:a.open[c],high:a.high[c],low:a.low[c],close:a.close[c],volume:a.volume?a.volume[c]:a.volume,timestamp:a.timestamp?a.timestamp[c]:a.timestamp});d.value&&(this.result.open.push(d.value.open),this.result.high.push(d.value.high),this.result.low.push(d.value.low),this.result.close.push(d.value.close),this.result.volume.push(d.value.volume),this.result.timestamp.push(d.value.timestamp))})}nextValue(a){var b=this.generator.next(a).value;return b}}J.calculate=l;class K extends w{constructor(a,b,c,d){if(super(),this.size=a,this.maintainHigh=b,this.maintainLow=c,this.maintainSum=d,this.totalPushed=0,this.periodHigh=0,this.periodLow=Infinity,this.periodSum=0,!a||'number'!=typeof a)throw'Size required and should be a number.';this._push=this.push,this.push=function(a){this.add(a),this.totalPushed++}}add(a){this.length===this.size?(this.lastShift=this.shift(),this._push(a),this.maintainHigh&&this.lastShift==this.periodHigh&&this.calculatePeriodHigh(),this.maintainLow&&this.lastShift==this.periodLow&&this.calculatePeriodLow(),this.maintainSum&&(this.periodSum-=this.lastShift)):this._push(a),this.maintainHigh&&this.periodHigh<=a&&(this.periodHigh=a),this.maintainLow&&this.periodLow>=a&&(this.periodLow=a),this.maintainSum&&(this.periodSum+=a)}*iterator(){for(this.resetCursor();this.next();)yield this.current}calculatePeriodHigh(){for(this.resetCursor(),this.next()&&(this.periodHigh=this.current);this.next();)this.periodHigh<=this.current&&(this.periodHigh=this.current)}calculatePeriodLow(){for(this.resetCursor(),this.next()&&(this.periodLow=this.current);this.next();)this.periodLow>=this.current&&(this.periodLow=this.current)}}class L extends u{constructor(a){super(a),this.result=[];var b=Object.assign({},{conversionPeriod:9,basePeriod:26,spanPeriod:52,displacement:26},a),c=new K(2*b.conversionPeriod,!0,!0,!1),d=new K(2*b.basePeriod,!0,!0,!1),e=new K(2*b.spanPeriod,!0,!0,!1);this.generator=function*(){let a,f,g=r(b.conversionPeriod,b.basePeriod,b.spanPeriod,b.displacement),h=1;for(f=yield;;){if(c.push(f.high),c.push(f.low),d.push(f.high),d.push(f.low),e.push(f.high),e.push(f.low),h<g)h++;else{let b=(c.periodHigh+c.periodLow)/2,f=(d.periodHigh+d.periodLow)/2,g=(e.periodHigh+e.periodLow)/2;a={conversion:b,base:f,spanA:(b+f)/2,spanB:g}}f=yield a}}(),this.generator.next(),a.low.forEach((b,c)=>{var d=this.generator.next({high:a.high[c],low:a.low[c]});d.value&&this.result.push(d.value)})}nextValue(a){return this.generator.next(a).value}}L.calculate=m;class M extends u{constructor(a){super(a);var b,c=a.useSMA?x:y,d=new c({period:a.maPeriod,values:[],format:(a)=>a}),e=new H({period:a.atrPeriod,high:[],low:[],close:[],format:(a)=>a});this.result=[],this.generator=function*(){var c;for(b=yield;;){var{close:f}=b,g=d.nextValue(f),h=e.nextValue(b);g!=void 0&&h!=void 0&&(c={middle:g,upper:g+a.multiplier*h,lower:g-a.multiplier*h}),b=yield c}}(),this.generator.next();var f=a.high;f.forEach((b,c)=>{var d={high:b,low:a.low[c],close:a.close[c]},e=this.generator.next(d);e.value!=void 0&&this.result.push(e.value)})}nextValue(a){var b=this.generator.next(a);if(b.value!=void 0)return b.value}}M.calculate=n;class N extends u{constructor(a){super(a);var b=a.high,c=a.low,d=a.close;this.result=[];var e=new H({period:a.period,high:[],low:[],close:[],format:(a)=>a}),f=new K(2*a.period,!0,!0,!1);this.generator=function*(){for(var b,c,d=yield;;){var{high:g,low:h}=d;f.push(g),f.push(h),c=e.nextValue(d),f.totalPushed>=2*a.period&&c!=void 0&&(b={exitLong:f.periodHigh-c*a.multiplier,exitShort:f.periodLow+c*a.multiplier}),d=yield b}}(),this.generator.next(),b.forEach((a,b)=>{var e={high:a,low:c[b],close:d[b]},f=this.generator.next(e);f.value!=void 0&&this.result.push(f.value)})}nextValue(a){var b=this.generator.next(a);if(b.value!=void 0)return b.value}}N.calculate=o,a.forceindex=d,a.vwap=e,a.volumeprofile=g,a.averagegain=h,a.averageloss=i,a.renko=k,a.heikinashi=l,a.fibonacciretracement=function(a,b){let c,d=[0,23.6,38.2,50,61.8,78.6,100,127.2,161.8,261.8,423.6];return c=a<b?d.map(function(c){let d=b-p(a-b)*c/100;return 0<d?d:0}):d.map(function(c){let d=b+p(a-b)*c/100;return 0<d?d:0}),c},a.ichimokucloud=m,a.keltnerchannels=n,a.KeltnerChannels=M,a.KeltnerChannelsInput=class extends t{constructor(){super(...arguments),this.maPeriod=20,this.atrPeriod=10,this.useSMA=!1,this.multiplier=1}},a.KeltnerChannelsOutput=class extends t{},a.chandelierexit=o,a.ChandelierExit=N,a.ChandelierExitInput=class extends t{constructor(){super(...arguments),this.period=22,this.multiplier=3}},a.ChandelierExitOutput=class extends t{}})(this.window=this.window||{});

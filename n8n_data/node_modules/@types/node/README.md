# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for Node.js (http://nodejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node/v6.

### Additional Details
 * Last updated: Fri, 16 Oct 2020 16:30:57 GMT
 * Dependencies: none
 * Global values: `<PERSON><PERSON><PERSON>`, `NodeJS`, `SlowBuffer`, `__dirname`, `__filename`, `clearImmediate`, `clearInterval`, `clearTimeout`, `console`, `exports`, `global`, `module`, `process`, `require`, `setImmediate`, `setInterval`, `setTimeout`

# Credits
These definitions were written by [Microsoft TypeScript](https://github.com/Microsoft), [DefinitelyTyped](https://github.com/DefinitelyTyped), [W<PERSON><PERSON>](https://github.com/WilcoBakker), [<PERSON>](https://github.com/inlined), [<PERSON>](https://github.com/eps1lon), [<PERSON>](https://github.com/Alorel), [<PERSON>àng <PERSON>](https://github.com/KSXGitHub), and [Sander Koenders](https://github.com/Archcry).

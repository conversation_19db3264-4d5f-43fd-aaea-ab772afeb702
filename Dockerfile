FROM n8nio/n8n:latest

USER root

# Install technicalindicators globally
RUN npm install -g technicalindicators

# Create a symlink to make it accessible to the node user
RUN ln -sf /usr/local/lib/node_modules/technicalindicators /home/<USER>/node_modules/technicalindicators || true

# Ensure proper permissions
RUN chown -R node:node /home/<USER>

# Switch back to node user
USER node

# Set the working directory
WORKDIR /home/<USER>

#!/usr/bin/env python3
import json
import sys

# Raw data from database
nodes_raw = '''[{"parameters":{"jsCode":"const articles = $json.articles || [];\\nconst filteredArticles = articles.map(article => ({\\n  title: article.title,\\n  description: article.description,\\n}));\\n\\nreturn [{\\n  json:{\\n    filteredArticles\\n  }\\n}]"},"type":"n8n-nodes-base.code","typeVersion":2,"position":[440,760],"id":"a2a01538-cadb-4212-beb3-2d61e5e73d3f","name":"trimming news"},{"parameters":{"numberInputs":3},"type":"n8n-nodes-base.merge","typeVersion":3.1,"position":[1080,340],"id":"6ef98fd7-b9c3-4ded-8904-d15997533940","name":"<PERSON><PERSON>","alwaysOutputData":false},{"parameters":{"modelId":{"__rl":true,"value":"deepseek-ai/deepseek-r1","mode":"list","cachedResultName":"DEEPSEEK-AI/DEEPSEEK-R1"},"messages":{"values":[{"content":"=You are a highly intelligent and accurate sentiment analyzer specializing in cryptocurrency markets. Analyze the sentiment of the provided text using a two-part approach: \\n\\n1. Short-Term Sentiment: \\n    -Evaluate the immediate market reaction, recent news impact, and technical volatility. \\n    -Determine a sentiment category\\"positive\\",\\"Neutral\\", or \\"Negative\\".\\n    -Calculate a numerical score between -1 (extremly negative) and 1 (extremely positive).\\n    -Provide a detailed rationale explaining the short-term sentiment. \\n\\n2. Long-Term Sentiment: \\n    -Evaluate the overall market outlook, fundamentals, and regulatory developments. \\n    -Determine the sentiment category: \\"Positive\\", \\"Neutral\\", or \\"Negative\\". \\n    -Calculate a numerical score between -1 (extremely negative) and 1 (extremely positive). \\n    -Provide a detailed rationale explaining the long-term sentiment. \\n\\nYour output must be exactly a JSON object with exactly two keys: \\"shortTermSentiment\\" and \\"longTermSentiment\\". Do not output anything else. \\n\\nFor example, your output should look like: {\\n  \\"shortTermSentiment\\": {\\n    \\"category\\": \\"Positive\\", \\n    \\"score\\": 0.7, \\n    \\"rationale\\": \\"...\\"\\n}, \\n  \\"longTermSentiment\\": {\\n    \\"category\\": \\"Neutral\\", \\n    \\"score\\": 0.1, \\n    \\"rationale\\": \\"...\\"\\n  }\\n}. \\nNow, analyze the following text and produce your JSON output: {{ JSON.stringify($json.filteredArticles) }}\\n\\n","role":"system"}]},"jsonOutput":true,"options":{}},"type":"@n8n/n8n-nodes-langchain.openAi","typeVersion":1.8,"position":[700,780],"id":"15c9a24d-ceaf-4cfd-9d98-9f7065def08d","name":"OpenAI","retryOnFail":true,"alwaysOutputData":true}]'''

connections_raw = '''{"trimming news":{"main":[[{"node":"OpenAI","type":"main","index":0}]]},"Merge":{"main":[[{"node":"combine JSON","type":"main","index":0}]]},"OpenAI":{"main":[[{"node":"檢驗","type":"main","index":0}]]}}'''

settings_raw = '''{"executionOrder":"v1"}'''

try:
    # Parse the JSON data
    nodes = json.loads(nodes_raw)
    connections = json.loads(connections_raw)
    settings = json.loads(settings_raw)
    
    # Create the complete workflow object
    workflow = {
        "id": "7EwjfAoKDkj7crK4",
        "name": "My workflow",
        "active": False,
        "nodes": nodes,
        "connections": connections,
        "settings": settings
    }
    
    # Write formatted JSON to file
    with open('My_workflow_FORMATTED.json', 'w', encoding='utf-8') as f:
        json.dump(workflow, f, indent=2, ensure_ascii=False)
    
    print("✅ 工作流 JSON 文件已成功格式化並保存為 'My_workflow_FORMATTED.json'")
    print(f"📊 包含 {len(nodes)} 個節點")
    print(f"🔗 包含 {len(connections)} 個連接組")
    
except json.JSONDecodeError as e:
    print(f"❌ JSON 解析錯誤: {e}")
except Exception as e:
    print(f"❌ 處理錯誤: {e}")

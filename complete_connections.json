{"trimming news": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "combine JSON", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "檢驗", "type": "main", "index": 0}]]}, "combine JSON": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "檢驗": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "HTTP Request 1h1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Code2", "type": "main", "index": 0}]]}, "Telegram Trigger1": {"main": [[{"node": "trimming trade pair", "type": "main", "index": 0}]]}, "HTTP Request 1d1": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "HTTP Request news1": {"main": [[{"node": "trimming news", "type": "main", "index": 0}]]}, "HTTP Request 15m1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Code2": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Edit Fields2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "trimming trade pair": {"main": [[{"node": "HTTP Request 1h1", "type": "main", "index": 0}, {"node": "HTTP Request 15m1", "type": "main", "index": 0}, {"node": "HTTP Request 1d1", "type": "main", "index": 0}, {"node": "HTTP Request news1", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Code": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}